#!/usr/bin/python3.9
# -*- coding: utf-8 -*-
from my_project.token import ExpiringTokenAuthentication
from django.db import connection
from django.views.decorators.csrf import csrf_exempt
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from my_app.models import SysLog, TtUploadFileData
from my_app.manage.taskManeger import TaskManager

import logging
logger = logging.getLogger('django')
from vgis_log.logTools import LoggerHelper
import datetime
from my_app.utils.snowflake_id_util import SnowflakeIDUtil
import json
from my_app.views.response.baseRespone import Result
# 导入模型（现在统一从models包导入）
from my_app.models import SysUser, SysUserToken, TtDroneTasks, TtDroneBasicInfo, TtDroneFlightRecords, TtDroneAlerts, TtWeatherData
from django.utils import timezone

class TaskViewSet(viewsets.ModelViewSet):

    permission_classes = (IsAuthenticated,)
    authentication_classes = (ExpiringTokenAuthentication,)

    @action(detail=False, methods=['POST'])
    def create_task(self, request, *args, **kwargs):
        api_path = "/zhbxfzcd_api/create_task/"
        try:
            function_title = "新增任务"
            start = LoggerHelper.set_start_log_info(logger)

            request.data["create_user_id"] = request.auth.user_id
            request.data["create_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            request.data["update_user_id"] = request.auth.user_id
            request.data["update_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request,
                                          function_title)

            request.data["id"] = SnowflakeIDUtil.snowflakeId()
            task_name = request.data["task_name"] if "task_name" in request.data else None
            if len(TtDroneTasks.objects.filter(task_name=task_name)) > 0:
                error_info = "任务名称:{}已存在，请换个名称".format(task_name)
                LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                           request.auth.user, request,
                                                           function_title, error_info, None)

                return Result.fail(error_info, error_info)
            TtDroneTasks.objects.create(**request.data)
            return Result.sucess("{}成功".format(function_title))
        except Exception as exp:
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            json_data = json.dumps(str(exp))

            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user,
                                                         request, function_title, None, exp)
            return Result.fail(error_info, json_data)

    @action(detail=False, methods=['POST'])
    def update_task(self, request, *args, **kwargs):
        try:
            function_title = "更新任务"
            start = LoggerHelper.set_start_log_info(logger)
            #  预警名称 alarm_name不能重名
            id = request.data["id"]
            api_path = request.path
            tis = TtDroneTasks.objects.filter(id=id)
            if len(tis) > 0:
                old_alarm_name = TtDroneTasks.objects.filter(id=id)[0].task_name

                request.data["update_user_id"] = request.auth.user_id
                request.data["update_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                task_name = request.data["task_name"] if "task_name" in request.data else None
                if task_name is not None and old_alarm_name != task_name and len(
                        TtDroneTasks.objects.filter(task_name=task_name)) > 0:
                    error_info = "任务名称:{}已存在，请换个名称".format(task_name)
                    LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                               request.auth.user, request,
                                                               function_title, error_info, None)
                    # json_data = json.dumps(error_info)
                    return Result.fail(error_info, error_info)
                else:
                    del request.data["id"]

                    tis.update(**request.data)

                    LoggerHelper.set_end_log_info(SysLog, logger, start, api_path + str(id), request.auth.user,
                                                  request,
                                                  function_title)
                    msg = "{}成功".format(function_title)
                    # json_data = json.dumps(msg)
                    return Result.sucess(msg, msg)
            else:
                error_info = "更新的主键ID号:{}不存在，请换个ID号".format(id)
                LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                           request.auth.user, request,
                                                           function_title, error_info, None)
                json_data = json.dumps(error_info)
                return Result.fail(error_info, json_data)
        except Exception as exp:
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path + str(id),
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            json_data = json.dumps(str(exp))
            return Result.fail(error_info, json_data)

    # 查询事件
    @action(detail=False, methods=['POST'])
    def get_task(self, request):
        api_path = request.path
        function_title = "查询任务信息"
        try:
            taskManager = TaskManager(connection)
            start = LoggerHelper.set_start_log_info(logger)

            static_value = taskManager.get_task(request)
            LoggerHelper.set_end_log_info(SysLog, logger, start, request.path, request.auth.user, request,
                                          function_title)
            msg = "{}成功".format(function_title)
            return Result.sucess(msg, static_value)
        except Exception as exp:
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            msg = "{}失败".format(function_title)
            return Result.fail(msg, str(exp))

    # 删除事件
    @action(detail=False, methods=['POST'])
    def del_task(self, request, *args, **kwargs):
        try:
            function_title = "删除任务信息"
            start = LoggerHelper.set_start_log_info(logger)
            #  预警名称 alarm_name不能重名
            id = request.data["id"]
            api_path = request.path
            TtDroneTasks.objects.filter(id=id).delete()
            msg = "{}成功".format(function_title)
            return Result.sucess(msg, msg)

        except Exception as exp:
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path + str(id),
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            json_data = json.dumps(str(exp))
            return Result.fail(error_info, json_data)

    # 无人机基础信息

    @action(detail=False, methods=['POST'])
    def create_dron_basic_info(self, request, *args, **kwargs):
        api_path = "/zhbxfzcd_api/create_dron_basic_info/"
        try:
            function_title = "新增无人机基础信息"
            start = LoggerHelper.set_start_log_info(logger)

            request.data["create_user_id"] = request.auth.user_id
            request.data["create_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            request.data["update_user_id"] = request.auth.user_id
            request.data["update_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request,
                                          function_title)

            request.data["drone_id"] = SnowflakeIDUtil.snowflakeId()
            drone_serial = request.data["drone_serial"] if "drone_serial" in request.data else None
            if len(TtDroneBasicInfo.objects.filter(drone_serial=drone_serial)) > 0:
                error_info = "无人机序列号:{}已存在，请换个名称".format(drone_serial)
                LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                           request.auth.user, request,
                                                           function_title, error_info, None)

                return Result.fail(error_info, error_info)
            TtDroneBasicInfo.objects.create(**request.data)
            return Result.sucess("{}成功".format(function_title))
        except Exception as exp:
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            json_data = json.dumps(str(exp))

            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user,
                                                         request, function_title, None, exp)
            return Result.fail(error_info, json_data)

    @action(detail=False, methods=['POST'])
    def update_dron_basic_info(self, request, *args, **kwargs):
        try:
            function_title = "更新无人机基础信息"
            start = LoggerHelper.set_start_log_info(logger)
            #  预警名称 alarm_name不能重名
            drone_id = request.data["drone_id"]
            api_path = request.path
            tis = TtDroneBasicInfo.objects.filter(drone_id=drone_id)
            if len(tis) > 0:
                old_alarm_name = TtDroneBasicInfo.objects.filter(drone_id=drone_id)[0].drone_serial

                request.data["update_user_id"] = request.auth.user_id
                request.data["update_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                drone_serial = request.data["drone_serial"] if "drone_serial" in request.data else None
                if drone_serial is not None and old_alarm_name != drone_serial and len(
                        TtDroneBasicInfo.objects.filter(drone_serial=drone_serial)) > 0:
                    error_info = "无人机序列号:{}已存在，请换个名称".format(drone_serial)
                    LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                               request.auth.user, request,
                                                               function_title, error_info, None)
                    # json_data = json.dumps(error_info)
                    return Result.fail(error_info, error_info)
                else:
                    del request.data["drone_id"]

                    tis.update(**request.data)

                    LoggerHelper.set_end_log_info(SysLog, logger, start, api_path + str(id), request.auth.user,
                                                  request,
                                                  function_title)
                    msg = "{}成功".format(function_title)
                    # json_data = json.dumps(msg)
                    return Result.sucess(msg, msg)
            else:
                error_info = "更新的主键ID号:{}不存在，请换个ID号".format(drone_id)
                LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                           request.auth.user, request,
                                                           function_title, error_info, None)
                json_data = json.dumps(error_info)
                return Result.fail(error_info, json_data)
        except Exception as exp:
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path + str(drone_id),
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            json_data = json.dumps(str(exp))
            return Result.fail(error_info, json_data)

    # 查询事件
    @action(detail=False, methods=['POST'])
    def get_dron_basic_info(self, request):
        api_path = request.path
        function_title = "查询无人机基础信息信息"
        try:
            taskManager = TaskManager(connection)
            start = LoggerHelper.set_start_log_info(logger)

            static_value = taskManager.get_drone_basic_info(request)
            LoggerHelper.set_end_log_info(SysLog, logger, start, request.path, request.auth.user, request,
                                          function_title)
            msg = "{}成功".format(function_title)
            return Result.sucess(msg, static_value)
        except Exception as exp:
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            msg = "{}失败".format(function_title)
            return Result.fail(msg, str(exp))

    # 删除事件
    @action(detail=False, methods=['POST'])
    def del_dron_basic_info(self, request, *args, **kwargs):
        try:
            function_title = "删除无人机基础信息信息"
            start = LoggerHelper.set_start_log_info(logger)
            #  预警名称 alarm_name不能重名
            drone_id = request.data["drone_id"]
            api_path = request.path
            TtDroneBasicInfo.objects.filter(drone_id=drone_id).delete()
            msg = "{}成功".format(function_title)
            return Result.sucess(msg, msg)

        except Exception as exp:
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path + str(drone_id),
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            json_data = json.dumps(str(exp))
            return Result.fail(error_info, json_data)


    # 飞行记录

    @action(detail=False, methods=['POST'])
    def create_flight_records(self, request, *args, **kwargs):
        api_path = "/zhbxfzcd_api/create_flight_records/"
        try:
            function_title = "新增飞行记录信息"
            start = LoggerHelper.set_start_log_info(logger)

            request.data["create_user_id"] = request.auth.user_id
            request.data["create_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            request.data["update_user_id"] = request.auth.user_id
            request.data["update_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request,
                                          function_title)

            request.data["flight_id"] = SnowflakeIDUtil.snowflakeId()
            request.data["is_del"] = 0
            # 获取今天的日期（不含时间）
            today = timezone.now().date()
            # 查询创建时间是今天的记录数量
            count = TtDroneFlightRecords.objects.filter(
                create_time__date=today
            ).count()+1
            # 获取今天的日期并格式化为YYYYMMDD
            today.strftime('%Y%m%d')
            # 生成格式化字符串
            request.data["flight_number"] = f"DRN-{today}-{count}"
            # drone_serial = request.data["drone_serial"] if "drone_serial" in request.data else None
            # if len(TtDroneFlightRecords.objects.filter(drone_serial=drone_serial)) > 0:
            #     error_info = "无人机序列号:{}已存在，请换个名称".format(drone_serial)
            #     LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
            #                                                request.auth.user, request,
            #                                                function_title, error_info, None)
            #
            #     return Result.fail(error_info, error_info)
            TtDroneFlightRecords.objects.create(**request.data)
            return Result.sucess("{}成功".format(function_title))
        except Exception as exp:
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            json_data = json.dumps(str(exp))

            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user,
                                                       request, function_title, None, exp)
            return Result.fail(error_info, json_data)

    @action(detail=False, methods=['POST'])
    def update_flight_records(self, request, *args, **kwargs):
        try:
            function_title = "更新飞行记录信息"
            start = LoggerHelper.set_start_log_info(logger)
            #  预警名称 alarm_name不能重名
            flight_id = request.data["flight_id"]
            api_path = request.path
            tis = TtDroneFlightRecords.objects.filter(flight_id=flight_id)
            if len(tis) > 0:

                request.data["update_user_id"] = request.auth.user_id
                request.data["update_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                del request.data["flight_id"]

                tis.update(**request.data)

                LoggerHelper.set_end_log_info(SysLog, logger, start, api_path + str(id), request.auth.user,
                                              request,
                                              function_title)
                msg = "{}成功".format(function_title)
                # json_data = json.dumps(msg)
                return Result.sucess(msg, msg)
            else:
                error_info = "更新的主键ID号:{}不存在，请换个ID号".format(flight_id)
                LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                           request.auth.user, request,
                                                           function_title, error_info, None)
                json_data = json.dumps(error_info)
                return Result.fail(error_info, json_data)
        except Exception as exp:
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path + str(flight_id),
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            json_data = json.dumps(str(exp))
            return Result.fail(error_info, json_data)

    # 查询事件
    @action(detail=False, methods=['POST'])
    def get_flight_records(self, request):
        api_path = request.path
        function_title = "查询飞行记录信息信息"
        try:
            taskManager = TaskManager(connection)
            start = LoggerHelper.set_start_log_info(logger)

            static_value = taskManager.get_flight_records(request)
            LoggerHelper.set_end_log_info(SysLog, logger, start, request.path, request.auth.user, request,
                                          function_title)
            msg = "{}成功".format(function_title)
            return Result.sucess(msg, static_value)
        except Exception as exp:
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            msg = "{}失败".format(function_title)
            return Result.fail(msg, str(exp))

    # 删除事件
    @action(detail=False, methods=['POST'])
    def del_flight_records(self, request, *args, **kwargs):
        api_path = request.path
        try:
            function_title = "删除飞行记录信息信息"
            start = LoggerHelper.set_start_log_info(logger)
            #  预警名称 alarm_name不能重名
            flight_id = request.data["flight_id"]

            tis = TtDroneFlightRecords.objects.filter(flight_id=flight_id)
            if len(tis) > 0:
                request.data["is_del"] = 1
                request.data["update_user_id"] = request.auth.user_id
                request.data["update_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                del request.data["flight_id"]

                tis.update(**request.data)

                LoggerHelper.set_end_log_info(SysLog, logger, start, api_path + str(id), request.auth.user,
                                              request,
                                              function_title)
                msg = "{}成功".format(function_title)
            msg = "{}成功".format(function_title)
            return Result.sucess(msg, msg)

        except Exception as exp:
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path + str(flight_id),
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            json_data = json.dumps(str(exp))
            return Result.fail(error_info, json_data)

    #创建警告数据
    @action(detail=False, methods=['POST'])
    def create_drone_alerts(self, request, *args, **kwargs):
        api_path = "/zhbxfzcd_api/create_drone_alerts/"
        try:
            function_title = "新增告警记录信息"
            start = LoggerHelper.set_start_log_info(logger)

            request.data["create_user_id"] = request.auth.user_id
            request.data["create_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            request.data["update_user_id"] = request.auth.user_id
            request.data["update_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request,
                                          function_title)

            request.data["alert_id"] = SnowflakeIDUtil.snowflakeId()


            TtDroneAlerts.objects.create(**request.data)
            return Result.sucess("{}成功".format(function_title))
        except Exception as exp:
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            json_data = json.dumps(str(exp))

            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path, request.auth.user,
                                                       request, function_title, None, exp)
            return Result.fail(error_info, json_data)

    @action(detail=False, methods=['POST'])
    def update_drone_alerts(self, request, *args, **kwargs):
        try:
            function_title = "更新告警记录信息"
            start = LoggerHelper.set_start_log_info(logger)
            #  预警名称 alarm_name不能重名
            alert_id = request.data["alert_id"]
            api_path = request.path
            tis = TtDroneAlerts.objects.filter(alert_id=alert_id)
            if len(tis) > 0:

                request.data["update_user_id"] = request.auth.user_id
                request.data["update_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                del request.data["alert_id"]

                tis.update(**request.data)

                LoggerHelper.set_end_log_info(SysLog, logger, start, api_path + str(id), request.auth.user,
                                              request,
                                              function_title)
                msg = "{}成功".format(function_title)
                # json_data = json.dumps(msg)
                return Result.sucess(msg, msg)
            else:
                error_info = "更新的主键ID号:{}不存在，请换个ID号".format(alert_id)
                LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                           request.auth.user, request,
                                                           function_title, error_info, None)
                json_data = json.dumps(error_info)
                return Result.fail(error_info, json_data)
        except Exception as exp:
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path + str(alert_id),
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            json_data = json.dumps(str(exp))
            return Result.fail(error_info, json_data)

    # 查询事件
    @action(detail=False, methods=['POST'])
    def get_drone_alerts(self, request):
        api_path = request.path
        function_title = "查询告警记录信息信息"
        try:
            taskManager = TaskManager(connection)
            start = LoggerHelper.set_start_log_info(logger)

            static_value = taskManager.get_drone_alerts(request)
            LoggerHelper.set_end_log_info(SysLog, logger, start, request.path, request.auth.user, request,
                                          function_title)
            msg = "{}成功".format(function_title)
            return Result.sucess(msg, static_value)
        except Exception as exp:
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            msg = "{}失败".format(function_title)
            return Result.fail(msg, str(exp))

    # 删除事件
    @action(detail=False, methods=['POST'])
    def del_drone_alerts(self, request, *args, **kwargs):
        api_path = request.path
        try:
            function_title = "删除告警记录信息信息"
            start = LoggerHelper.set_start_log_info(logger)
            #  预警名称 alarm_name不能重名
            alert_id = request.data["alert_id"]

            tis = TtDroneAlerts.objects.filter(alert_id=alert_id)
            if len(tis) > 0:

                request.data["update_user_id"] = request.auth.user_id
                request.data["update_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                # del request.data["alert_id"]

                tis.delete()

                LoggerHelper.set_end_log_info(SysLog, logger, start, api_path + str(id), request.auth.user,
                                              request,
                                              function_title)
                msg = "{}成功".format(function_title)
            msg = "{}成功".format(function_title)
            return Result.sucess(msg, msg)

        except Exception as exp:
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path + str(alert_id),
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            json_data = json.dumps(str(exp))
            return Result.fail(error_info, json_data)

    @action(detail=False, methods=['POST'])
    def get_alerts_files(self, request):
        api_path = request.path
        function_title = "查询告警数据信息"
        try:
            taskManager = TaskManager(connection)
            start = LoggerHelper.set_start_log_info(logger)

            static_value = taskManager.get_alerts_files(request)
            LoggerHelper.set_end_log_info(SysLog, logger, start, request.path, request.auth.user, request,
                                          function_title)
            msg = "{}成功".format(function_title)
            return Result.sucess(msg, static_value)
        except Exception as exp:
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            msg = "{}失败".format(function_title)
            return Result.fail(msg, str(exp))

    # 创建天气
    @action(detail=False, methods=['POST'])
    def create_weather_data(self, request, *args, **kwargs):
        api_path = "/zhbxfzcd_api/create_weather_data/"
        try:
            function_title = "新增天气信息"
            start = LoggerHelper.set_start_log_info(logger)

            request.data["create_user_id"] = request.auth.user_id
            request.data["create_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            request.data["weather_id"] = SnowflakeIDUtil.snowflakeId()

            TtWeatherData.objects.create(**request.data)
            return Result.sucess("{}成功".format(function_title))
        except Exception as exp:
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            json_data = json.dumps(str(exp))

            return Result.fail(error_info, json_data)

    @action(detail=False, methods=['POST'])
    def update_weather_data(self, request, *args, **kwargs):
        try:
            function_title = "更新天气信息"
            start = LoggerHelper.set_start_log_info(logger)
            #  预警名称 alarm_name不能重名
            weather_id = request.data["weather_id"]
            api_path = request.path
            tis = TtWeatherData.objects.filter(weather_id=weather_id)
            if len(tis) > 0:



                del request.data["weather_id"]

                tis.update(**request.data)

                LoggerHelper.set_end_log_info(SysLog, logger, start, api_path + str(id), request.auth.user,
                                              request,
                                              function_title)
                msg = "{}成功".format(function_title)
                # json_data = json.dumps(msg)
                return Result.sucess(msg, msg)
            else:
                error_info = "更新的主键ID号:{}不存在，请换个ID号".format(weather_id)
                LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                           request.auth.user, request,
                                                           function_title, error_info, None)
                json_data = json.dumps(error_info)
                return Result.fail(error_info, json_data)
        except Exception as exp:
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path + str(weather_id),
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            json_data = json.dumps(str(exp))
            return Result.fail(error_info, json_data)

    # 查询事件
    @action(detail=False, methods=['POST'])
    def get_weather_data(self, request):
        api_path = request.path
        function_title = "查询天气信息"
        try:
            taskManager = TaskManager(connection)
            start = LoggerHelper.set_start_log_info(logger)

            static_value = taskManager.get_weather_data(request)
            LoggerHelper.set_end_log_info(SysLog, logger, start, request.path, request.auth.user, request,
                                          function_title)
            msg = "{}成功".format(function_title)
            return Result.sucess(msg, static_value)
        except Exception as exp:
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            msg = "{}失败".format(function_title)
            return Result.fail(msg, str(exp))

    # 删除事件
    @action(detail=False, methods=['POST'])
    def del_weather_data(self, request, *args, **kwargs):
        api_path = request.path
        try:
            function_title = "删除天气信息"
            start = LoggerHelper.set_start_log_info(logger)
            #  预警名称 alarm_name不能重名
            weather_id = request.data["weather_id"]

            tis = TtWeatherData.objects.filter(weather_id=weather_id)
            if len(tis) > 0:
                request.data["update_user_id"] = request.auth.user_id
                request.data["update_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                # del request.data["weather_id"]

                tis.delete()

                LoggerHelper.set_end_log_info(SysLog, logger, start, api_path + str(id), request.auth.user,
                                              request,
                                              function_title)
                msg = "{}成功".format(function_title)
            msg = "{}成功".format(function_title)
            return Result.sucess(msg, msg)

        except Exception as exp:
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path + str(weather_id),
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            error_info = "{}失败，可能原因是{}".format(function_title, str(exp))
            json_data = json.dumps(str(exp))
            return Result.fail(error_info, json_data)

    # 告警完成率统计
    @action(detail=False, methods=['POST'])
    def get_alert_group(self, request):
        api_path = request.path
        start = LoggerHelper.set_start_log_info(logger)
        function_title = "查询告警记录信息"
        try:
            taskManager = TaskManager(connection)

            static_value = taskManager.get_alert_group(request)

            msg = "{}成功".format(function_title)
            return Result.sucess(msg, static_value)
        except Exception as exp:
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            msg = "{}失败".format(function_title)
            return Result.fail(msg, str(exp))

    # 降本增效
    @action(detail=False, methods=['POST'])
    def get_jbzx(self, request):
        api_path = request.path
        function_title = "查询降本增效信息"
        start = LoggerHelper.set_start_log_info(logger)
        try:
            taskManager = TaskManager(connection)

            static_value = taskManager.get_jbzx(request)

            msg = "{}成功".format(function_title)
            return Result.sucess(msg, static_value)
        except Exception as exp:
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            msg = "{}失败".format(function_title)
            return Result.fail(msg, str(exp))

    # 数据成果
    @action(detail=False, methods=['POST'])
    def get_sjcg(self, request):
        api_path = request.path
        function_title = "查询数据成果信息"
        start = LoggerHelper.set_start_log_info(logger)
        try:
            taskManager = TaskManager(connection)

            static_value = taskManager.get_sjcg(request)

            msg = "{}成功".format(function_title)
            return Result.sucess(msg, static_value)
        except Exception as exp:
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                       request.auth.user, request,
                                                       function_title, None, exp)
            msg = "{}失败".format(function_title)
            return Result.fail(msg, str(exp))