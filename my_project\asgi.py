"""
ASGI config for my_project project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/3.1/howto/deployment/asgi/
"""

import os
from django.core.asgi import get_asgi_application
from django.conf import settings
from django.contrib.staticfiles.handlers import ASGIStaticFilesHandler

# 设置环境变量
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'my_project.settings')

# 先初始化Django应用
django_asgi_app = get_asgi_application()

# 在开发环境中添加静态文件处理
if settings.DEBUG:
    django_asgi_app = ASGIStaticFilesHandler(django_asgi_app)

# 然后导入需要Django模型的模块
from channels.routing import ProtocolTypeRouter, URLRouter
from . import routings
from channels.security.websocket import AllowedHostsOriginValidator
from my_app.websocket.drone_comsumers import TokenAuthMiddleware

# 创建ASGI应用
application = ProtocolTypeRouter(
    {
        'http': django_asgi_app,
        'websocket': AllowedHostsOriginValidator(
            TokenAuthMiddleware(
                URLRouter(routings.websocket_urlpatterns)
            )
        )
    }
)