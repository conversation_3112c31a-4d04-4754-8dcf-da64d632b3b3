# -*- coding: utf-8 -*-
"""
my_app.models 包初始化文件
统一导出所有模型，保持向后兼容性

重构说明：
- system_models.py: 包含所有系统核心模型（从原models.py迁移）
- wrj_model.py: 包含无人机业务模型（从my_models迁移）
- dict_model.py: 包含字典模型（从my_models迁移）
- legacy_models.py: 包含遗留模型（从models2.py迁移） 遗留
- ywmodel.py: 包含业务模型（从ywmodel.py迁移） 遗留

这样重构后，现有的导入语句 `from my_app.models import ModelName` 仍然可以正常工作
"""

# 导入系统核心模型
from .system_models import (
    # 认证和用户相关
    AuthUser, SysUser, SysUserRole, SysUserToken,

    # 系统配置相关
    SysConfig, SysParam, SysMenu, SysRole, SysRoleMenu, SysLog, SysOss, SysDepartment,

    # 文件上传相关
    TtUploadFileData, TtTaskSltFileData, TtDronBaskFileData, TtDronAlertFileData,

    # 区域相关
    TmDdistrict, TmRegion, TmBingtuan,

    # 标注和POI相关
    TtAnnotationSysmbolData, TtAnnotationType, TtFeatureAnnotationData,
    TtFriendFoeInfo, TtIntelligenceSource, TtPoiData, TtViewBookmarkData,
    TtPointType, TtFieldType, TtFeatureAnnotationeNewFields,

    # 服务相关
    TtServiceData, TtServiceInterfaceType, TtKeyPointAreaData,
)

# 导入业务模型（从my_models迁移过来的）
from .wrj_model import (
    TtDroneTasks, TtDroneBasicInfo, TtDroneFlightRecords, TtDroneAlerts, TtWeatherData
)

# 导入字典模型（从my_models迁移过来的）
from .dict_model import TtSelectDict

from .ywmodel import *

# 导出所有模型，保持完整的向后兼容性
__all__ = [
    # 认证和用户相关
    'AuthUser', 'SysUser', 'SysUserRole', 'SysUserToken',

    # 系统配置相关
    'SysConfig', 'SysParam', 'SysMenu', 'SysRole', 'SysRoleMenu', 'SysLog', 'SysOss', 'SysDepartment',

    # 文件上传相关
    'TtUploadFileData', 'TtTaskSltFileData', 'TtDronBaskFileData', 'TtDronAlertFileData',

    # 区域相关
    'TmDdistrict', 'TmRegion', 'TmBingtuan',

    # 标注和POI相关
    'TtAnnotationSysmbolData', 'TtAnnotationType', 'TtFeatureAnnotationData',
    'TtFriendFoeInfo', 'TtIntelligenceSource', 'TtPoiData', 'TtViewBookmarkData',
    'TtPointType', 'TtFieldType', 'TtFeatureAnnotationeNewFields',

    # 服务相关
    'TtServiceData', 'TtServiceInterfaceType', 'TtKeyPointAreaData',

    # 业务模型（无人机相关）
    'TtDroneTasks', 'TtDroneBasicInfo', 'TtDroneFlightRecords', 'TtDroneAlerts', 'TtWeatherData',

    # 字典模型
    'TtSelectDict',
]
