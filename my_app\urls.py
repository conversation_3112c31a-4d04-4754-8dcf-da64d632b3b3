#!/usr/bin/python3.9
# -*- coding: utf-8 -*-
# @Time    : 2021/2/17 20:53
# <AUTHOR> gisfan_ai
# @Email   : <EMAIL>
# @File    : urls.py
# @Desc    ：路由器
# @Software: PyCharm

# from django.conf.urls import include
from django.urls import path, include
from rest_framework.routers import DefaultRouter

from my_app.views.businessViews import TtUploadFileDataViewSet, TmDdistrictViewSet, TtAnnotationSysmbolDataViewSet, \
    TtAnnotationTypeViewSet, TtFeatureAnnotationDataViewSet, TtFriendFoeInfoViewSet, TtIntelligenceSourceViewSet, \
    TtPoiDataViewSet, TtViewBookmarkDataViewSet, TtPointTypeViewSet, TmBingtuanViewSet, TtFieldTypeViewSet, \
    TtServiceInterfaceTypeViewSet, TtKeyPointAreaDataViewSet
from my_app.views.sysViews import SysConfigViewSet, SysDepartmentViewSet, SysLogViewSet, SysMenuViewSet, \
    SysOssViewSet, SysRoleViewSet, SysRoleMenuViewSet, SysUserViewSet, SysUserRoleViewSet, SysUserTokenViewSet, \
    AuthUserViewSet, SysDictViewSet, SysParamViewSet, TtServiceDataViewSet
from my_app.views.userViews import UserViewSet
from my_app.views.ywuView import YewuView,kongdiView,zongheView
from my_app.views.QdictViews import QdictViewSet
from my_app.views.taskView import TaskViewSet


#报告管理
from my_app.views.bgglView import BgglView



#备份
from my_app.views.dataBaseBak import DataBaseBakViewSet
router = DefaultRouter()
# 用户表（已废弃）路由器---20250226 由于个人信息需要修改，故启用这个接口模块
router.register(r'user', UserViewSet, basename='user')
# # 系统配置表路由器
# router.register(r'sysConfig', SysConfigViewSet, basename='sysConfig')
# # 系统部门表路由器
# router.register(r'sysDepartment', SysDepartmentViewSet, basename='sysDepartment')
# # 系统日志路由器
# router.register(r'sysLog', SysLogViewSet, basename='sysLog')
# # 系统菜单表路由器
# router.register(r'sysMenu', SysMenuViewSet, basename='sysMenu')
# # 系统定制表由器
# router.register(r'sysOss', SysOssViewSet, basename='sysOss')
# # 系统角色表由器
# router.register(r'sysRole', SysRoleViewSet, basename='sysRole')
# # 系统角色菜单表由器
# router.register(r'sysRoleMenu', SysRoleMenuViewSet, basename='sysRoleMenu')
# # 系统用户表由器
# router.register(r'authUser', AuthUserViewSet, basename='authUser')
# # 系统用户表（已废弃）由器
# router.register(r'sysUser', SysUserViewSet, basename='sysUser')
# # 系统用户角色表由器
# router.register(r'sysUserRole', SysUserRoleViewSet, basename='sysUserRole')
# # 系统用户token表由器
# router.register(r'sysUserToken', SysUserTokenViewSet, basename='sysUserToken')
# # 系统字典管理由器
# router.register(r'sysDict', SysDictViewSet, basename='sysDict')
# # 系统参数管理由器
# router.register(r'sysParam', SysParamViewSet, basename='sysParam')
# # 上传文件表由器
router.register(r'ttUploadFileData', TtUploadFileDataViewSet, basename='ttUploadFileData')
# # 行政区划表由器
# router.register(r'tmDdistrict', TmDdistrictViewSet, basename='tmDdistrict')
# router.register(r'qdict', QdictViewSet, basename='qdict')
router.register(r'task', TaskViewSet, basename='task')



# router.register("zh",zongheView,basename="zh")




#获取备份文件
# router.register("dataBaseBak",DataBaseBakViewSet,basename="dataBaseBak")



# 根据项目名称修改url
urlpatterns = [
    path('wrj_api/', include(router.urls), name='wrj_api')
]
